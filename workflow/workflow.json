{"nodes": [{"parameters": {}, "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-5180, 2660], "id": "1ba3cb32-fea4-48ae-9b22-8296115f4407"}, {"parameters": {"jsCode": "// Central configuration for the entire workflow\nreturn {\n  // Workflow configuration\n  type: \"new-topics\", // Options: \"upto-date\", \"new-topics\", \"new-topics-rewrite\"\n\n  // RSS feeds and data sources\n  sources: [\n    {\n      provider: \"rss\",\n      url: \"https://bair.berkeley.edu/blog/feed.xml\",\n    },\n    {\n      provider: \"rss\",\n      url: \"https://machinelearningmastery.com/blog/feed/\",\n    },\n    {\n      provider: \"facebook\",\n      url: \"https://www.facebook.com/abdurrahmantalha.dev\",\n    },\n  ],\n\n  // Other settings\n  maxItems: 10,\n  daysBack: 7,\n  singleUserProvider: \"facebook\",\n  singleUserUrl: \"https://www.facebook.com/abdurrahmantalha.dev\",\n  multipleUsers: [\n    {\n      provider: \"linkedin\",\n      url: \"https://www.linkedin.com/in/gary-marcus-b6384b4/recent-activity/all/\",\n      name: \"<PERSON>\",\n    },\n    {\n      provider: \"linkedin\",\n      url: \"https://www.linkedin.com/in/simonsinek/recent-activity/all/\",\n      name: \"<PERSON>\",\n    },\n  ],\n\n  writerType: \"single-writer\"\n};\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4960, 2660], "id": "29a2318f-90bf-4ca9-a11b-5abaf0d9e388", "name": "Central Configuration"}, {"parameters": {"jsCode": "// Split sources into individual items for processing\nconst config = $input.first().json;\nreturn config.sources.map(source => ({\n  ...source,\n  config: {\n    type: config.type,\n    maxItems: config.maxItems,\n    daysBack: config.daysBack\n  }\n}));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4740, 2660], "id": "067f7be8-dd5a-46db-88dc-bd12584ad3e0", "name": "Split Sources for Processing"}, {"parameters": {"jsCode": "// Wait for all feed processing to complete, then trigger topic generation\nconst allItems = $input.all();\nconst config = allItems[0].json.config;\n\n// Return single item to trigger next phase\nreturn [{ \n  message: \"Feed processing complete\",\n  config: config,\n  processedCount: allItems.length\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3540, 2640], "id": "11b6159f-b42a-4b2c-b164-243e7a0a68d4", "name": "Feeds Processing Complete", "executeOnce": true}, {"parameters": {"jsCode": "const rows = $input.all();\nconst config = $('Feeds Processing Complete').first().json.config;\nconst daysBack = config.daysBack || 7;\n\nconst cutoffDate = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000);\n\n// Filter for recent posts\nconst recentRows = rows.filter(row => {\n  const pubDate = new Date(row.json.pubDate);\n  return pubDate >= cutoffDate;\n});\n\n// Sort by pubDate descending (most recent first)\nrecentRows.sort((a, b) => {\n  return new Date(b.json.pubDate) - new Date(a.json.pubDate);\n});\n\nreturn recentRows;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2640, 3200], "id": "e2422c5d-7f5d-4557-85ec-40848255a81d", "name": "Filter <PERSON> Posts"}, {"parameters": {"jsCode": "console.log($input.all()[0]);\n\nreturn $input.all()[0].json.output.map((e) => {\n  return { ...e, custom_instructions: \"\", approved:false };\n});\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1840, 3200], "id": "bbaad089-e1f6-47b8-8654-32486dbfcdcb", "name": "Format output"}, {"parameters": {"url": "=http://localhost:3000/scrape/{{ $json.provider }}?url={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-4520, 2660], "id": "a225076b-6e89-4c69-97a9-cfb4f1b4987a", "name": "Get Feed Output1"}, {"parameters": {"model": "mistral-medium", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "typeVersion": 1, "position": [-4300, 2880], "id": "f28f6d97-e719-4d06-a7d9-************", "name": "Mistral Cloud Chat Model8", "credentials": {"mistralCloudApi": {"id": "hHfxjXLNpd2kwE9n", "name": "Mistral Cloud account"}}}, {"parameters": {"model": "mistral-medium", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "typeVersion": 1, "position": [-2180, 3420], "id": "b33eec1e-9c77-4704-b95b-835fbe01fbb4", "name": "Mistral Cloud Chat Model9", "credentials": {"mistralCloudApi": {"id": "hHfxjXLNpd2kwE9n", "name": "Mistral Cloud account"}}}, {"parameters": {"jsonSchemaExample": "[\n  {\n    \"title\": \"Example Topic 1\",\n    \"description\": \"A short description explaining the topic and its relevance.\",\n    \"total_engagement\": 0\n  },\n  {\n    \"title\": \"Example Topic 2\",\n    \"description\": \"A short description explaining the topic and its relevance.\",\n    \"total_engagement\": 0\n  }\n]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [-2060, 3420], "id": "eee64a45-ccb1-4e01-a42a-379de236c96a", "name": "Structured Output Parser1"}, {"parameters": {"promptType": "define", "text": "Imagine that you are a content summarizer, summarize the following content on what this is. Do not include any extra text or content, just the pure summary. Try to keep the summary within 5-6 lines.\n\nHere is the post: {{ $json.content }}", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-4300, 2660], "id": "1e150947-974d-4c27-9a01-f9d0d83e0728", "name": "Summarize Posts"}, {"parameters": {"assignments": {"assignments": [{"id": "f8dfa3f2-d68e-4bda-9521-6d86942061a1", "name": "title", "value": "={{ $('Get Feed Output1').item.json.title }}", "type": "string"}, {"id": "24980d2a-304f-407b-b0f5-ce5be603a1b6", "name": "link", "value": "={{ $('Get Feed Output1').item.json.link }}", "type": "string"}, {"id": "9cd566f6-4342-4199-9eff-d2c8351316f7", "name": "pubDate", "value": "={{ $('Get Feed Output1').item.json.pubDate }}", "type": "string"}, {"id": "1c2642d4-3e09-4185-a75d-440fc49bf276", "name": "provider", "value": "={{ $('Get Feed Output1').item.json.provider }}", "type": "string"}, {"id": "a56ec14d-b492-4553-8664-4670fdb03bc7", "name": "content", "value": "={{ $json.output }}", "type": "string"}, {"id": "65b13273-04d3-4b85-9be3-099aa2562c48", "name": "likes", "value": "={{ $('Get Feed Output1').item.json?.engagement?.likes || 0}}", "type": "number"}, {"id": "edcdb361-0b47-46e7-bedb-38ba3f3633f4", "name": "shares", "value": "={{ $('Get Feed Output1').item.json?.engagement?.shares || 0 }}", "type": "number"}, {"id": "aa2a7332-338a-442a-94bd-da73b646ddeb", "name": "comments", "value": "={{ $('Get Feed Output1').item.json?.engagement?.comments || 0 }}", "type": "number"}, {"id": "174593cf-2186-49d9-9b7b-8dc16b3a90cf", "name": "total_engagement", "value": "={{ \n  Number(\n    (\n      ($('Get Feed Output1').item.json?.engagement?.comments || 0) +\n      ($('Get Feed Output1').item.json?.engagement?.shares || 0) +\n      ($('Get Feed Output1').item.json?.engagement?.likes || 0)\n    )\n  ) || 0 \n}}", "type": "number"}, {"id": "config-passthrough", "name": "config", "value": "={{ $('Split Sources for Processing').item.json.config }}", "type": "object"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-3940, 2660], "id": "941198fc-2e2e-486f-bdff-7bec55e91bf3", "name": "Format Output"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU", "mode": "id"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Feed Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU/edit#gid=**********"}, "columns": {"mappingMode": "autoMapInputData", "value": {}, "matchingColumns": ["link"], "schema": [{"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "link", "displayName": "link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "pubDate", "displayName": "pubDate", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "provider", "displayName": "provider", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "content", "displayName": "content", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "likes", "displayName": "likes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "shares", "displayName": "shares", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "comments", "displayName": "comments", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-3740, 2640], "id": "612990b8-e0e3-456d-8b98-8797a5859e40", "name": "Save scraped posts", "credentials": {"googleSheetsOAuth2Api": {"id": "iKgsGak52Kf2F5yE", "name": "Google Sheets account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU", "mode": "id"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Feed Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU/edit#gid=**********"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-2860, 3200], "id": "1be98c2c-f1d5-4df9-aa91-5a711e158723", "name": "Get scraped posts", "credentials": {"googleSheetsOAuth2Api": {"id": "iKgsGak52Kf2F5yE", "name": "Google Sheets account"}}}, {"parameters": {"operation": "clear", "documentId": {"__rl": true, "value": "1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU", "mode": "id"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Generated Topics", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU/edit#gid=**********"}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-1620, 3200], "id": "afa0ecc4-b6ea-475d-83b7-c72aef7008f9", "name": "Clear Existing Topics", "credentials": {"googleSheetsOAuth2Api": {"id": "iKgsGak52Kf2F5yE", "name": "Google Sheets account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU", "mode": "id"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Generated Topics", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU/edit#gid=**********"}, "columns": {"mappingMode": "defineBelow", "value": {"title": "={{ $json.title }}", "description": "={{ $json.description }}", "total_engagement": "={{ $json.total_engagement }}"}, "matchingColumns": ["title"], "schema": [{"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "total_engagement", "displayName": "total_engagement", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"useAppend": true}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-1400, 3200], "id": "5d6ef676-c139-43cc-8a63-a3884965cbef", "name": "Add new trending topics", "credentials": {"googleSheetsOAuth2Api": {"id": "iKgsGak52Kf2F5yE", "name": "Google Sheets account"}}}, {"parameters": {"maxItems": "={{ $('Feeds Processing Complete').first().json.config.maxItems || 10 }}"}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-2420, 3200], "id": "9cb3e865-e7a1-4f18-aefe-fe7a0c1bfd29", "name": "Limit posts to generate topics based on"}, {"parameters": {"promptType": "define", "text": "={{ $('Feeds Processing Complete').first().json.config.type === \"upto-date\" \n? \n`You are an expert AI research assistant. Given the following data, return only the exact original post topic in the same JSON format as below — do not create or modify topics.\n\n**Instructions:**\n* Extract only the original post topic, description, and the total_engagement from the provided data.\n* \"total_engagement\" must exactly match the number provided in the data.\n* Return them as an array of objects with the following format:\n[\n  {\n    \"title\": \"Original Post Title\",\n    \"description\": \"A short description explaining the topic and its relevance.\",\n    \"total_engagement\": 0\n  }\n]\n\n**Important:** This field is mandatory — every object must have \"total_engagement\".`\n: \n`You are an expert AI research assistant. Given the following data, generate 5 distinct suitable topics from the posts that could go viral based on the category of AI or Leadership\n\n**Instructions:**\n\n* Generate **at least 5 distinct topics** inspired by the above data. Also return the topic of the post inside of the result.\n* Each topic should contain:\n\n  1. **Title:** A concise, descriptive title for the topic.\n  2. **Description:** A short explanation of what the topic is about and why it is interesting or relevant.\n  3. **total_engagement:** Set to 0 for new topics.\n* Return the topics in valid **JSON format**, with each topic as an object containing \"title\", \"description\", and \"total_engagement\".\n* Make sure the topics cover a range of perspectives (technical, application-focused, security, AI modeling, biomedical, generative AI, etc.).\n\n**Expected JSON structure:**\n\n[\n  {\n    \"title\": \"Example Topic 1\",\n    \"description\": \"A short description explaining the topic and its relevance.\",\n    \"total_engagement\": 0\n  },\n  {\n    \"title\": \"Example Topic 2\",\n    \"description\": \"A short description explaining the topic and its relevance.\",\n    \"total_engagement\": 0\n  }\n]`\n}}\n\n**Data:**\n{{ $input.all().map(item => {\n    return `Title: ${item.json.title}\ntotal_engagement: ${item.json.total_engagement}\nSummary:\n${item.json.content}\n------------------------------`;\n  }).join(\"\\n\\n\"); }}", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-2200, 3200], "id": "333702c5-1839-4bd7-8d03-97b3391336ac", "name": "Generate Topics Based On Configuration", "executeOnce": true}, {"parameters": {"content": "## Scrape and save post from  RSS Feeds, Facebook, Twitter, Linkedin", "height": 480, "width": 1880}, "type": "n8n-nodes-base.stickyNote", "position": [-5240, 2520], "typeVersion": 1, "id": "781fd5ce-0ca9-44f6-af66-c8e03aef141b", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Generate new topics or figure out existing topics to keep profile updated", "height": 580, "width": 1920, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [-3040, 3000], "typeVersion": 1, "id": "75074b8f-bce7-4007-b041-99a9ff4c8b2c", "name": "Sticky Note1"}, {"parameters": {"jsCode": "const allPosts = $input.all();\n\n// Group posts by topic\nconst postsByTopic = {};\nallPosts.forEach(post => {\n  const topic = post.json[\"Topic\"] || \"Unknown Topic\";\n  if (!postsByTopic[topic]) {\n    postsByTopic[topic] = [];\n  }\n  postsByTopic[topic].push(post);\n});\n\nconst MAX_VARIATIONS = 3; // Limit to 2-3 variations per message\nconst messages = [];\n\nfor (const [topic, posts] of Object.entries(postsByTopic)) {\n  let message = `🚀 **New Post Variations for Topic: ${topic}**\\n\\n`;\n\n  // Take only the first MAX_VARIATIONS posts\n  const limitedPosts = posts.slice(0, MAX_VARIATIONS);\n\n  limitedPosts.forEach((post, index) => {\n    const content = post.json['Generated Content'] || post.json.output;\n    const variation = post.json[\"Variation\"] || 'unknown';\n    message += `**${index + 1}. ${variation}:**\\n${content}\\n\\n---\\n\\n`;\n  });\n\n  message += `📝 **Total Variations Shown:** ${limitedPosts.length} of ${posts.length}\\n\\n`;\n  message += `✅ React with ✅ to approve and post all shown variations\\n`;\n  message += `❌ React with ❌ to reject and skip posting`;\n\n  messages.push({\n    json: {\n      topic: topic,\n      message: message,\n      totalVariations: limitedPosts.length,\n      posts: posts\n    }\n  });\n}\n\nreturn messages;\n"}, "id": "837c9719-eba8-4d89-9b36-152aefb1f366", "name": "Group All Variations", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2460, 4380]}, {"parameters": {"resource": "message", "operation": "sendAndWait", "guildId": {"__rl": true, "value": "1126214048028037120", "mode": "list", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s server", "cachedResultUrl": "https://discord.com/channels/1126214048028037120"}, "channelId": {"__rl": true, "value": "1402230271990038648", "mode": "list", "cachedResultName": "goflow-testing", "cachedResultUrl": "https://discord.com/channels/1126214048028037120/1402230271990038648"}, "message": "={{ $json.message }}", "approvalOptions": {"values": {"approvalType": "double"}}, "options": {}}, "type": "n8n-nodes-base.discord", "typeVersion": 2, "position": [-2020, 4300], "id": "6a8d3887-a6db-4856-af45-57ed0167d4d4", "name": "Send Approval Request", "webhookId": "72fbdd58-c435-4199-995b-3f4e4cef6d3e", "credentials": {"discordBotApi": {"id": "gWGhP2n3HYGdWy4k", "name": "Discord Bot account"}}}, {"parameters": {"jsCode": "// Get the approved posts from the previous node\nconst approvalData = $('Loop Over Items').first().json;\nconst allPosts = approvalData.posts;\nconst config = $('Central Configuration2').first().json;\nconst botUsers = config.botUsers;\n\n// Create combinations of posts and bot accounts\nconst postingTasks = [];\n\n// Distribute posts across bot accounts\nallPosts.forEach((post, index) => {\n  // Round-robin assignment or you can implement other logic\n  const botUserIndex = index % botUsers.length;\n  const selectedBot = botUsers[botUserIndex];\n\n  console.log(post)\n  \n  postingTasks.push({\n    content: post.json[\"Generated Content\"] || post.json.output,\n    variation: post.json[\"Variation\"],\n    botUser: selectedBot,\n    topic: post.json[\"Topic\"],\n  });\n});\n\nreturn postingTasks.map(task => ({ json: task }));"}, "id": "13d365ee-8baf-43a2-957d-7e6896877615", "name": "Prepare Posting Tasks", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1580, 4300]}, {"parameters": {"method": "POST", "url": "=http://localhost:3000/post/{{ $json.botUser.provider }}", "sendBody": true, "bodyParameters": {"parameters": [{"name": "cookies", "value": "={{ $json.botUser.data }}"}, {"name": "text", "value": "={{ $json.content }}"}]}, "options": {}}, "id": "b1feba75-685f-413f-8457-382c609e488c", "name": "Post to Social Media", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-1360, 4300], "retryOnFail": true}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU", "mode": "id"}, "sheetName": {"__rl": true, "value": "Posted Content", "mode": "name"}, "columns": {"mappingMode": "autoMapInputData", "value": {"Content": "={{ $('Prepare Posting Tasks').item.json.content }}", "Variation": "={{ $('Prepare Posting Tasks').item.json.variation }}", "Bot User ID": "={{ $('Prepare Posting Tasks').item.json.botUserId }}", "Provider": "={{ $('Prepare Posting Tasks').item.json.provider }}", "Posted At": "={{ new Date().toISOString() }}", "Status": "={{ $json.statusCode === 200 ? 'success' : 'failed' }}", "Topic": "={{ $('Group All Variations').first().json.topic }}"}, "matchingColumns": [], "schema": [{"id": "Content", "displayName": "Content", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Variation", "displayName": "Variation", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Bot User ID", "displayName": "Bot User ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Provider", "displayName": "Provider", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Posted At", "displayName": "Posted At", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Response", "displayName": "Response", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "8fa6ab04-6cbf-4029-b1ba-3333649169ca", "name": "Log Posted Content", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [-920, 4380], "credentials": {"googleSheetsOAuth2Api": {"id": "iKgsGak52Kf2F5yE", "name": "Google Sheets account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "08ea828a-98ec-413a-8576-64805f4e2e70", "leftValue": "={{ $('Central Configuration2').first().json.writerType }}", "rightValue": "single-writer", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-4720, 4380], "id": "fab2aaf5-8614-46dd-ba59-3b2859045680", "name": "Single User or Multi User?1"}, {"parameters": {"jsCode": "// Single user configuration\nconst config = $('Central Configuration2').first().json;\nreturn [{\n  provider: config.singleUserProvider,\n  url: config.singleUserUrl\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4500, 4280], "id": "d862620c-b25a-4179-954a-a42c8a5d24d4", "name": "Set Single User1"}, {"parameters": {"jsCode": "// Multiple users configuration\nconst config = $('Set Configuration2').first().json;\nreturn config.multipleUsers;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4500, 4480], "id": "6155ef11-5b61-4d43-9d04-9b2f474500ef", "name": "Set Multiple Users1"}, {"parameters": {"url": "=http://localhost:3000/scrape/{{ $json.provider }}?url={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-4280, 4380], "id": "6deb7dd4-d757-481d-a17a-f43537587942", "name": "Get User Posts1"}, {"parameters": {"jsCode": "// Check if we're in single user mode\nconst config = $('Central Configuration2').first().json;\n\nif (config.type === 'upto-date') {\n  // Single user mode - merge topics with user posts\n  const topicsData = $('Get Trending Topics1').all();\n  const topClusters = topicsData\n    .map(i => i.json)\n    .sort((a, b) => parseInt(a.total_engagement) - parseInt(b.total_engagement));\n  return topClusters;\n} else {\n  // Multi user mode - group posts by URL\n  const posts = $input.all();\n  const groupedArray = Object.values(\n    posts.reduce((acc, post) => {\n      if (!acc[post.json.originalUrl]) {\n        acc[post.json.originalUrl] = { url: post.json.originalUrl, posts: [] };\n      }\n      acc[post.json.originalUrl].posts.push(post.json);\n      return acc;\n    }, {})\n  );\n  return groupedArray;\n}"}, "id": "31b33ddd-b4d4-4d77-bcb9-df28f0a6a800", "name": "Process Data1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4060, 4380]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "08ea828a-98ec-413a-8576-64805f4e2e70", "leftValue": "={{ $('Central Configuration2').first().json.writerType }}", "rightValue": "single-writer", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-3620, 4380], "id": "95f63791-57b5-4b67-87d5-60f1548fc234", "name": "Route to AI Agent1"}, {"parameters": {"promptType": "define", "text": "=# Enhanced Facebook Post Generation Prompt\n\nCreate a Facebook post about: **{{ $json.title }}** in English.\nTopic Description: {{ $json.description }}\n\n\n## Writing Style Analysis & Instructions\n\n**Reference Posts for Style Mimicking:**\n{{ $('Get User Posts1').all().slice(0, 5).map(item => item.json.content).join(\"\\n-------\\n\") }}\n\n### Style Mimicking Guidelines\n\nAnalyze and replicate these specific elements from the reference posts:\n\n1. **Sentence Structure & Length**: Match typical sentence patterns (short vs. long, simple vs. complex).\n2. **Vocabulary & Word Choice**: Use similar complexity level, slang, technical terms, or casual language.\n3. **Punctuation Habits**: Replicate their use of ellipses, exclamation points, question marks, emojis.\n4. **Paragraph Structure**: Mirror their line breaks, spacing, and post organization.\n5. **Engagement Style**: Copy their way of addressing the audience (direct, storytelling, questions, etc.).\n6. **Emotional Tone**: Match their energy level (enthusiastic, contemplative, humorous, professional).\n7. **Personal Voice Elements**: Include similar personal touches, opinions, or storytelling approaches.\n8. **Call-to-Action Style**: If they typically ask questions or encourage interaction, maintain that pattern.\n9. **Hashtag, Emoji Usage or Emotes**: If they use hashtags, emojis, or emotes, include them in a similar manner.\n10. **Avoid using —**\n\n## Content Requirements\n\n* Address the specified topic naturally within their established voice.\n* Maintain authenticity – the post should sound like it genuinely came from the original author.\n* Keep Facebook best practices in mind (engaging, shareable, appropriate length).\n* Include relevant engagement elements if that matches their style (questions, polls, etc.).\n* Use hashtags, mentions, or other social elements only if they typically do.\n\n## Information Provided by the User\n\nHere are some information provided by the user, use these information when writing the post:\n**{{ $json.custom_instructions }}**\n\n## Output Requirements\n\nGenerate multiple variations of the same post in different tones but in the same writing style:\n\n* Positive\n* Negative\n* Neutral/objective\n* Enthusiastic/energetic\n* Sarcastic/ironic\n* Professional/formal\n* Casual/friendly\n\n## Output Format\n\nProvide ONLY the Facebook post content, grouped by tone variation.\nDo not include explanations, commentary, or meta-text. Each variation should be ready to copy and paste directly into Facebook.\n\nExample Output:\n\n```json\n[\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Extremely positive\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Extremely negative\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Neutral/objective\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Enthusiastic/energetic\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Sarcastic/ironic\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Professional/formal\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Casual/friendly\",\n    \"topic\": \"Topic Title\"\n  }\n]\n```", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-3400, 4080], "id": "120cfff5-772f-4837-9975-beb7d16161f2", "name": "AI Agent - Single User Style1", "executeOnce": false}, {"parameters": {"promptType": "define", "text": "=# Enhanced Facebook Post Generation Prompt\n\nCreate a Facebook post about: {{ $('Get ').all()[0].json.title }} in english\n\n## Writing Style Analysis & Instructions\n\n**Reference Posts for Style Mimicking:**\n{{ $json.posts.slice(0, 10).map(item => item.content).join(\"\\n-------\\n\") }}\n\n## Style Mimicking Guidelines\n\n**Analyze and replicate these specific elements from the reference posts:**\n\n1. **Sentence Structure & Length**: Match the typical sentence patterns (short vs. long, simple vs. complex)\n2. **Vocabulary & Word Choice**: Use similar complexity level, slang, technical terms, or casual language\n3. **Punctuation Habits**: Replicate their use of ellipses, exclamation points, question marks, emojis\n4. **Paragraph Structure**: Mirror their line breaks, spacing, and post organization\n5. **Engagement Style**: Copy their way of addressing the audience (direct, storytelling, questions, etc.)\n6. **Emotional Tone**: Match their energy level (enthusiastic, contemplative, humorous, professional)\n7. **Personal Voice Elements**: Include similar personal touches, opinions, or storytelling approaches\n8. **Call-to-Action Style**: If they typically ask questions or encourage interaction, maintain that pattern\n9. **Hashtag, Emoji Usage or emotes**: If they use hashtags, emojis or emotes, include them in a similar manner \n10. **Avoid using —**\n\n## Content Requirements\n\n- Address the specified topic naturally within their established voice\n- Maintain authenticity - the post should sound like it genuinely came from the original author\n- Keep Facebook best practices in mind (engaging, shareable, appropriate length)\n- Include relevant engagement elements if that matches their style (questions, polls, etc.)\n- Use hashtags, mentions, or other social elements only if they typically do\n\n## Information provided by the user\n\nHere are some information provided by the user, use these information when writing the post:\n\n{{ $json.custom_instructions }}\n\n## Output Format\n\nProvide ONLY the Facebook post content with no additional commentary, explanations, or meta-text. The post should be ready to copy and paste directly into Facebook.", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-3400, 4580], "id": "955caeee-6e79-4081-b70f-f1a7940b4221", "name": "AI Agent - Multi User Style1", "executeOnce": false}, {"parameters": {"promptType": "define", "text": "=Here are some posts written in multiple different writing styles. Merge these posts together along with their writing styles to generate a new, blended writing style and create a post about the same topic.\n\nGenerate multiple variations of the same post as well in multiple different tones such as:\n\n* Extremely positive\n* Extremely negative\n* Neutral/objective\n* Enthusiastic/energetic\n* Sarcastic/ironic\n* Professional/formal\n* Casual/friendly\n\nDo not include any explanations or commentary, only the generated posts.\n\n{{ $input.all().map(item => item.json.content).join(\"\\n\\n--------\\n\\n\") }}\n\nExample output:\n\nExample Output:\n\n```json\n[\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Extremely positive\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Extremely negative\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Neutral/objective\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Enthusiastic/energetic\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Sarcastic/ironic\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Professional/formal\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Casual/friendly\",\n    \"topic\": \"Topic Title\"\n  }\n]\n```", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-3040, 4580], "id": "1d3f2b72-844a-4036-bdcb-b8a4b795aba7", "name": "AI Agent - Style Merger1", "executeOnce": true}, {"parameters": {"model": "mistral-medium", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "typeVersion": 1, "position": [-3380, 4300], "id": "86f38e81-1301-4284-998b-14d6104f03de", "name": "Mistral Cloud Chat Model", "credentials": {"mistralCloudApi": {"id": "hHfxjXLNpd2kwE9n", "name": "Mistral Cloud account"}}}, {"parameters": {"jsCode": "const data = $input.all();\n\nconst results = data.flatMap(item =>\n  item.json.output.map(subItem => ({\n    json: {\n      content: subItem.content,\n      variation: subItem.variation,\n      generatedAt: new Date().toISOString(),\n      topic: subItem.topic,\n    },\n  }))\n);\n\nreturn results;\n"}, "id": "0914028c-c3e2-4d54-82a1-13f71ef27d42", "name": "Format Post", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2980, 4180]}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU", "mode": "id"}, "sheetName": {"__rl": true, "value": "Generated Posts", "mode": "name"}, "columns": {"mappingMode": "defineBelow", "value": {"Generated Content": "={{ $json.content || $json.output }}", "Generated At": "={{ $json.generatedAt || new Date().toISOString() }}", "Variation": "={{ $json.variation || 'unknown' }}", "Status": "pending_approval", "Topic": "={{ $json.topic || \"Unkown\" }}"}, "matchingColumns": [], "schema": [{"id": "Post ID", "displayName": "Post ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Topic", "displayName": "Topic", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Generated Content", "displayName": "Generated Content", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Generated At", "displayName": "Generated At", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Variation", "displayName": "Variation", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Posted Account", "displayName": "Posted Account", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "9e1f9906-c85a-450f-a4da-2f81d348e63d", "name": "Save Generated Posts3", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [-2680, 4380], "credentials": {"googleSheetsOAuth2Api": {"id": "iKgsGak52Kf2F5yE", "name": "Google Sheets account"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.data.approved }}", "value2": true}]}}, "id": "********-28ae-4b50-bf77-833638d0eea2", "name": "IF Approved?1", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1800, 4300]}, {"parameters": {"model": "mistral-medium", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "typeVersion": 1, "position": [-3320, 4800], "id": "71a037b6-f124-4562-bfe9-7e76865911dd", "name": "Mistral Cloud Chat Model10", "credentials": {"mistralCloudApi": {"id": "hHfxjXLNpd2kwE9n", "name": "Mistral Cloud account"}}}, {"parameters": {"model": "mistral-medium", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "typeVersion": 1, "position": [-3020, 4800], "id": "80ff2eca-eb60-4b9c-a091-48f578fcee5b", "name": "Mistral Cloud Chat Model11", "credentials": {"mistralCloudApi": {"id": "hHfxjXLNpd2kwE9n", "name": "Mistral Cloud account"}}}, {"parameters": {"content": "## Generate posts based on either one single person's writing style or multiple different users writing style", "height": 1000, "width": 4440, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-5220, 3940], "typeVersion": 1, "id": "e578d039-a9dc-4572-93fc-15094b57b9ee", "name": "Sticky Note3"}, {"parameters": {"documentId": {"__rl": true, "value": "1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU", "mode": "id"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Generated Topics", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU/edit#gid=**********"}, "filtersUI": {"values": [{"lookupColumn": "approved", "lookupValue": "true"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-3840, 4380], "id": "4282ae66-97d8-4877-960e-b512422eaa6c", "name": "Get Trending Topics1", "credentials": {"googleSheetsOAuth2Api": {"id": "iKgsGak52Kf2F5yE", "name": "Google Sheets account"}}}, {"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-5160, 4380], "id": "a5075e5e-ae5d-4422-be9a-8f1554537e5d", "name": "Schedule Trigger1"}, {"parameters": {"jsCode": "// Central configuration for the entire workflow\nreturn {\n  // Workflow configuration\n  type: \"new-topics\", // Options: \"upto-date\", \"new-topics\", \"new-topics-rewrite\"\n\n  // RSS feeds and data sources\n  sources: [\n    {\n      provider: \"rss\",\n      url: \"https://bair.berkeley.edu/blog/feed.xml\",\n    },\n    {\n      provider: \"rss\",\n      url: \"https://machinelearningmastery.com/blog/feed/\",\n    },\n    {\n      provider: \"facebook\",\n      url: \"https://www.facebook.com/abdurrahmantalha.dev\",\n    },\n  ],\n\n  // Other settings\n  maxItems: 10,\n  daysBack: 7,\n  singleUserProvider: \"facebook\",\n  singleUserUrl: \"https://www.facebook.com/abdurrahmantalha.dev/\",\n  multipleUsers: [\n    {\n      provider: \"linkedin\",\n      url: \"https://www.linkedin.com/in/gary-marcus-b6384b4/recent-activity/all/\",\n      name: \"<PERSON>\",\n    },\n    {\n      provider: \"linkedin\",\n      url: \"https://www.linkedin.com/in/simonsinek/recent-activity/all/\",\n      name: \"<PERSON>\",\n    },\n  ],\n\n  writerType: \"single-writer\",\n\n  botUsers: [\n    {\n      id: \"nazmulatif\", // just to keep track who posted\n      provider: \"facebook\",\n      data: [\n        {\n          domain: \".facebook.com\",\n          expirationDate: **********.692558,\n          hostOnly: false,\n          httpOnly: true,\n          name: \"datr\",\n          path: \"/\",\n          sameSite: \"no_restriction\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value: \"1RowZwMsnafw6DFYQ2228q1t\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: **********.475331,\n          hostOnly: false,\n          httpOnly: true,\n          name: \"sb\",\n          path: \"/\",\n          sameSite: \"no_restriction\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value: \"1RowZ56xsyg-j3SiHgA8qAZD\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: **********.901264,\n          hostOnly: false,\n          httpOnly: false,\n          name: \"c_user\",\n          path: \"/\",\n          sameSite: \"no_restriction\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value: \"100083492924506\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: 1776567928.943017,\n          hostOnly: false,\n          httpOnly: true,\n          name: \"ps_l\",\n          path: \"/\",\n          sameSite: \"lax\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value: \"1\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: 1776567928.943092,\n          hostOnly: false,\n          httpOnly: true,\n          name: \"ps_n\",\n          path: \"/\",\n          sameSite: \"no_restriction\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value: \"1\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: 1773549756,\n          hostOnly: false,\n          httpOnly: false,\n          name: \"fbl_st\",\n          path: \"/\",\n          sameSite: \"strict\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value: \"100734808%3BT%3A29033562\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: 1760804571.475043,\n          hostOnly: false,\n          httpOnly: true,\n          name: \"b_user\",\n          path: \"/\",\n          sameSite: \"lax\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value: \"61578203685035\",\n        },\n        {\n          domain: \".facebook.com\",\n          hostOnly: false,\n          httpOnly: true,\n          name: \"ar_debug\",\n          path: \"/\",\n          sameSite: \"no_restriction\",\n          secure: true,\n          session: true,\n          storeId: \"0\",\n          value: \"1\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: 1787420605.296191,\n          hostOnly: false,\n          httpOnly: false,\n          name: \"i_user\",\n          path: \"/\",\n          sameSite: \"no_restriction\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value: \"61579095122195\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: 1756493249,\n          hostOnly: false,\n          httpOnly: false,\n          name: \"wd\",\n          path: \"/\",\n          sameSite: \"lax\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value: \"1920x911\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: **********.901492,\n          hostOnly: false,\n          httpOnly: true,\n          name: \"xs\",\n          path: \"/\",\n          sameSite: \"no_restriction\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value:\n            \"6%3A1Kp8WBo6lap7YQ%3A2%3A1742007924%3A-1%3A-1%3A0VhH9iTqMeGruQ%3AAcXrRKzHIwM5ZURzRndeE4qX3uYWhamNObi6NlY0GLU\",\n        },\n        {\n          domain: \".facebook.com\",\n          expirationDate: **********.725952,\n          hostOnly: false,\n          httpOnly: true,\n          name: \"fr\",\n          path: \"/\",\n          sameSite: \"no_restriction\",\n          secure: true,\n          session: false,\n          storeId: \"0\",\n          value:\n            \"1qOzYZBJx7tCVdR60.AWeuQTk-WGFCB1VLZ3dYURdMZej9aLBnpDj7N9aJyMr1MNV6f0o.BoqLM1..AAA.0.0.BoqLs9.AWdKiuVWHQkR1TbC3xtKgIQImog\",\n        },\n      ],\n    },\n  ],\n  testPlatform: \"facebook\",\n  testType: \"bot-accounts\"\n};\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4940, 4380], "id": "4ad6bd6e-9809-4e67-bdcf-d6d798020e95", "name": "Central Configuration2"}, {"parameters": {"jsonSchemaExample": "[\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Extremely positive\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Extremely negative\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Neutral/objective\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Enthusiastic/energetic\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Sarcastic/ironic\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Professional/formal\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Casual/friendly\",\n    \"topic\": \"Topic Title\"\n  }\n]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [-3260, 4300], "id": "d4193d50-27e0-4080-978e-014e1ea88f75", "name": "Structured Output Parser3"}, {"parameters": {"jsonSchemaExample": "[\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Extremely positive\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Extremely negative\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Neutral/objective\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Enthusiastic/energetic\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Sarcastic/ironic\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Professional/formal\",\n    \"topic\": \"Topic Title\"\n  },\n  {\n    \"content\": \"Post Text\",\n    \"variation\": \"Casual/friendly\",\n    \"topic\": \"Topic Title\"\n  }\n]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [-2900, 4800], "id": "afb385b8-04a5-43e6-ba31-ebe2250ab5bb", "name": "Structured Output Parser4"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2240, 4380], "id": "0b219817-1b81-405e-b3d5-e5e4f0dad11a", "name": "Loop Over Items"}, {"parameters": {"jsCode": "return $('Prepare Posting Tasks')\n  .all()\n  .map((e) => {\n    return {\n      json: {\n        content: e.json.content,\n        topic: e.json.topic,\n        botUserId: e.json.botUser.id,\n        provider: e.json.botUser.provider,\n        postedAt: new Date().toISOString(),\n        status: 'success',\n        likes: 0,\n        shares: 0,\n        comments: 0,\n      },\n    };\n  });\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1140, 4300], "id": "28c60c43-6ec0-413c-b533-e45aea83638a", "name": "Code"}, {"parameters": {"jsCode": "// Split configuration into individual profiles for processing\nconst config = $input.first().json;\nconst profiles = config.facebookProfiles;\n\nreturn profiles.map(profile => ({\n  ...config,\n  currentProfile: profile,\n  scrapeUrl: `${config.scrapeApiBaseUrl}/scrape/facebook?url=${encodeURIComponent(profile.url)}`\n}));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3500, 5220], "id": "51fa4891-5b15-47d2-9f4e-edc50e5d174c", "name": "Split Profiles"}, {"parameters": {"url": "={{ $json.scrapeUrl }}", "options": {"timeout": 30000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3300, 5220], "id": "9eaccf42-7518-44b2-91e2-7b49bf6da8b6", "name": "Scrape Facebook Posts"}, {"parameters": {"jsCode": "// Process scraped Facebook posts and prepare for comparison\nconst config = $('Split Profiles').item.json;\nconst scrapedData = $input.first().json;\n\n// Extract posts from the scraped data\n// Adjust this based on your API response structure\nlet posts = [];\n\nif (scrapedData && scrapedData.posts) {\n  posts = scrapedData.posts;\n} else if (Array.isArray(scrapedData)) {\n  posts = scrapedData;\n} else if (scrapedData.data && Array.isArray(scrapedData.data)) {\n  posts = scrapedData.data;\n}\n\n// Filter posts by date if specified\nconst daysBack = config.daysBack;\nconst cutoffDate = new Date();\ncutoffDate.setDate(cutoffDate.getDate() - daysBack);\n\nconst processedPosts = posts\n  .filter(post => {\n    if (post.publishedAt || post.created_time || post.timestamp) {\n      const postDate = new Date(post.publishedAt || post.created_time || post.timestamp);\n      return postDate >= cutoffDate;\n    }\n    return true; // Include posts without date info\n  })\n  .slice(0, config.maxPostsPerProfile)\n  .map(post => ({\n    // Standardize post structure\n    postId: post.id || post.postId || post.post_id,\n    content: post.text || post.content || post.message || '',\n    postUrl: post.url || post.link || post.permalink_url,\n    publishedAt: post.publishedAt || post.created_time || post.timestamp,\n    likes: parseInt(post.likes || post.like_count || 0),\n    shares: parseInt(post.shares || post.share_count || 0),\n    comments: parseInt(post.comments || post.comment_count || 0),\n    reactions: parseInt(post.reactions || post.reaction_count || post.likes || 0),\n    profileId: config.currentProfile.profileId,\n    profileName: config.currentProfile.profileName,\n    lastUpdated: new Date().toISOString(),\n    // Keep original data for reference\n    _originalData: post\n  }));\n\nreturn processedPosts.map(post => ({\n  ...config,\n  scrapedPost: post\n}));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3100, 5220], "id": "1a4b822b-8633-4d48-80fc-e5cfc58b8db6", "name": "Process Scraped Posts"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-2900, 5220], "id": "443d85bd-ffe8-441a-b530-051b310eabd3", "name": "Rate Limit Delay", "webhookId": "0ea65dd3-bc68-4579-9882-849d95400f63"}, {"parameters": {"documentId": {"__rl": true, "value": "=1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU", "mode": "id"}, "sheetName": {"__rl": true, "value": "Posted Content", "mode": "name"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [-2700, 5220], "id": "1e0c8ac4-e9a8-4cc8-ba9f-b3832d7dc2c9", "name": "Read Google Sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "iKgsGak52Kf2F5yE", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// Compare scraped posts with existing sheet data and determine actions\nconst config = $('Rate Limit Delay').item.json;\nconst scrapedPost = config.scrapedPost;\nconst sheetData = $input.first().json;\n\n// Convert sheet data to array if it's not already\nconst existingRows = Array.isArray(sheetData) ? sheetData : [sheetData];\n\n// Find matching row in sheet\nfunction findMatchingRow(post, rows) {\n  const criteria = config.matchingCriteria;\n  \n  // Try primary key match first\n  if (post.postId) {\n    const match = rows.find(row => row[config.googleSheet.columns.postId] === post.postId);\n    if (match) return match;\n  }\n  \n  // Try fallback matching\n  for (const key of criteria.fallbackKeys) {\n    if (key === 'content' && post.content) {\n      // Fuzzy content matching\n      const match = rows.find(row => {\n        const sheetContent = row[config.googleSheet.columns.content] || '';\n        const similarity = calculateSimilarity(post.content, sheetContent);\n        return similarity >= criteria.contentSimilarityThreshold;\n      });\n      if (match) return match;\n    }\n    \n    if (key === 'publishedAt' && post.publishedAt) {\n      const match = rows.find(row => {\n        const sheetDate = row[config.googleSheet.columns.publishedAt];\n        return new Date(post.publishedAt).getTime() === new Date(sheetDate).getTime();\n      });\n      if (match) return match;\n    }\n  }\n  \n  return null;\n}\n\nfunction calculateSimilarity(str1, str2) {\n  // Simple similarity calculation\n  const longer = str1.length > str2.length ? str1 : str2;\n  const shorter = str1.length > str2.length ? str2 : str1;\n  \n  if (longer.length === 0) return 1.0;\n  \n  const editDistance = levenshteinDistance(longer, shorter);\n  return (longer.length - editDistance) / longer.length;\n}\n\nfunction levenshteinDistance(str1, str2) {\n  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));\n  \n  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;\n  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;\n  \n  for (let j = 1; j <= str2.length; j++) {\n    for (let i = 1; i <= str1.length; i++) {\n      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;\n      matrix[j][i] = Math.min(\n        matrix[j][i - 1] + 1,\n        matrix[j - 1][i] + 1,\n        matrix[j - 1][i - 1] + indicator\n      );\n    }\n  }\n  \n  return matrix[str2.length][str1.length];\n}\n\nconst matchingRow = findMatchingRow(scrapedPost, existingRows);\nconst columns = config.googleSheet.columns;\n\nlet action = '';\nlet updateData = {};\nlet isEngagementChanged = false;\n\nif (matchingRow) {\n  // Post exists, check if engagement data needs updating\n  const currentLikes = parseInt(matchingRow[columns.likes] || 0);\n  const currentShares = parseInt(matchingRow[columns.shares] || 0);\n  const currentComments = parseInt(matchingRow[columns.comments] || 0);\n  const currentReactions = parseInt(matchingRow[columns.reactions] || 0);\n  \n  isEngagementChanged = \n    currentLikes !== scrapedPost.likes ||\n    currentShares !== scrapedPost.shares ||\n    currentComments !== scrapedPost.comments ||\n    currentReactions !== scrapedPost.reactions;\n  \n  if (isEngagementChanged || config.updateMode === 'full-update') {\n    action = 'update';\n    updateData = {\n      [columns.likes]: scrapedPost.likes,\n      [columns.shares]: scrapedPost.shares,\n      [columns.comments]: scrapedPost.comments,\n      [columns.reactions]: scrapedPost.reactions,\n      [columns.lastUpdated]: scrapedPost.lastUpdated\n    };\n    \n    if (config.updateMode === 'full-update') {\n      updateData = {\n        ...updateData,\n        [columns.content]: scrapedPost.content,\n        [columns.postUrl]: scrapedPost.postUrl,\n        [columns.publishedAt]: scrapedPost.publishedAt\n      };\n    }\n  } else {\n    action = 'skip';\n  }\n} else {\n  // New post, add to sheet\n  if (config.updateMode !== 'engagement-only') {\n    action = 'insert';\n    updateData = {\n      [columns.postId]: scrapedPost.postId,\n      [columns.content]: scrapedPost.content,\n      [columns.postUrl]: scrapedPost.postUrl,\n      [columns.publishedAt]: scrapedPost.publishedAt,\n      [columns.likes]: scrapedPost.likes,\n      [columns.shares]: scrapedPost.shares,\n      [columns.comments]: scrapedPost.comments,\n      [columns.reactions]: scrapedPost.reactions,\n      [columns.profileId]: scrapedPost.profileId,\n      [columns.profileName]: scrapedPost.profileName,\n      [columns.lastUpdated]: scrapedPost.lastUpdated\n    };\n  } else {\n    action = 'skip';\n  }\n}\n\nreturn [{\n  ...config,\n  scrapedPost,\n  matchingRow,\n  action,\n  updateData,\n  isEngagementChanged,\n  _comparison: {\n    found: !!matchingRow,\n    engagementChanged: isEngagementChanged,\n    action: action\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2500, 5220], "id": "6e6eb3a2-577a-42b2-964c-1ee536ca61b8", "name": "Compare & Determine Action"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "action-update", "leftValue": "={{ $json.action }}", "rightValue": "update", "operator": {"type": "string", "operation": "equals"}}, {"id": "action-insert", "leftValue": "={{ $json.action }}", "rightValue": "insert", "operator": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2300, 5220], "id": "c6550647-ae3c-41f4-a279-57d8f41daddf", "name": "Filter Actions"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "={{ $json.googleSheet.documentId }}", "mode": "id"}, "sheetName": {"__rl": true, "value": "={{ $json.googleSheet.sheetName }}", "mode": "name"}, "columns": {"mappingMode": "autoMapInputData", "value": "={{ $json.updateData }}", "matchingColumns": [{"id": "={{ $json.googleSheet.columns.postId }}", "displayName": "={{ $json.googleSheet.columns.postId }}", "type": "string", "defaultMatch": true, "canBeUsedToMatch": true}], "schema": [], "attemptToConvertTypes": true}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [-2100, 5120], "id": "cea5a5cb-5372-4fab-9773-7dff5319584c", "name": "Update Existing Row", "credentials": {"googleSheetsOAuth2Api": {"id": "iKgsGak52Kf2F5yE", "name": "Google Sheets account"}}}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-1900, 5220], "id": "4312d004-8d11-458f-bb7c-f866a0f8a158", "name": "Sheet Update Delay", "webhookId": "7f73e882-04bb-4ef4-8108-a880620bf299"}, {"parameters": {"jsCode": "// Generate summary report of the workflow execution\nconst allItems = $input.all();\n\nconst summary = {\n  totalProfiles: 0,\n  totalPostsScraped: 0,\n  totalUpdated: 0,\n  totalInserted: 0,\n  totalSkipped: 0,\n  profileSummaries: [],\n  executionTime: new Date().toISOString(),\n  errors: []\n};\n\nconst profileStats = {};\n\n// Aggregate statistics\nallItems.forEach(item => {\n  const data = item.json;\n  const profileId = data.scrapedPost?.profileId || 'unknown';\n  \n  if (!profileStats[profileId]) {\n    profileStats[profileId] = {\n      profileId: profileId,\n      profileName: data.scrapedPost?.profileName || 'Unknown',\n      postsScraped: 0,\n      updated: 0,\n      inserted: 0,\n      skipped: 0\n    };\n    summary.totalProfiles++;\n  }\n  \n  const stats = profileStats[profileId];\n  stats.postsScraped++;\n  summary.totalPostsScraped++;\n  \n  switch(data.action) {\n    case 'update':\n      stats.updated++;\n      summary.totalUpdated++;\n      break;\n    case 'insert':\n      stats.inserted++;\n      summary.totalInserted++;\n      break;\n    default:\n      stats.skipped++;\n      summary.totalSkipped++;\n  }\n});\n\nsummary.profileSummaries = Object.values(profileStats);\n\n// Log summary to console\nconsole.log('Facebook Scraping Workflow Summary:', JSON.stringify(summary, null, 2));\n\nreturn [summary];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1700, 5220], "id": "a24f9c02-cf75-414d-aa89-2b6150b87965", "name": "Generate Summary Report"}, {"parameters": {"jsCode": "// Central configuration for Facebook scraping and Google Sheets updating\nreturn {\n  // API Configuration\n  scrapeApiBaseUrl: \"http://localhost:3000\",\n  \n  // Google Sheets Configuration\n  googleSheet: {\n    documentId: \"1ag7dDPkAJ918f2kMc2UkQnFzdzls__QuGf-eE4jpWcU\", // Your existing sheet ID\n    sheetName: \"Facebook Posts\", // Name of the sheet tab\n    columns: {\n      content: \"Content\",\n      postId: \"Post ID\",\n      postUrl: \"Post URL\",\n      publishedAt: \"Published At\",\n      likes: \"Likes\",\n      shares: \"Shares\",\n      comments: \"Comments\",\n      reactions: \"Reactions\",\n      profileId: \"Profile ID\",\n      profileName: \"Profile Name\",\n      lastUpdated: \"Last Updated\"\n    }\n  },\n  \n  // Facebook Profiles to scrape\n  facebookProfiles: [\n    {\n      profileId: \"61579095122195\",\n      profileName: \"Nazmulatif\",\n      url: \"https://www.facebook.com/profile.php?id=61579095122195\"\n    },\n    {\n      profileId: \"100083492924506\", \n      profileName: \"Another Profile\",\n      url: \"https://www.facebook.com/profile.php?id=100083492924506\"\n    }\n    // Add more profiles as needed\n  ],\n  \n  // Scraping settings\n  maxPostsPerProfile: 20,\n  daysBack: 30, // Only process posts from last 30 days\n  \n  // Update settings\n  updateMode: \"engagement-only\", // Options: \"engagement-only\", \"full-update\", \"new-posts-only\"\n  batchSize: 10, // Process posts in batches\n  \n  // Matching criteria for comparing posts\n  matchingCriteria: {\n    primaryKey: \"postId\", // Primary matching field\n    fallbackKeys: [\"content\", \"publishedAt\"], // Fallback matching if postId not available\n    contentSimilarityThreshold: 0.85 // For fuzzy content matching\n  },\n  \n  // Rate limiting\n  rateLimiting: {\n    delayBetweenProfiles: 2000, // 2 seconds between profile scrapes\n    delayBetweenSheetUpdates: 500 // 0.5 seconds between sheet updates\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3700, 5220], "id": "57bd490e-82dc-4d15-a140-501bfb92cf1f", "name": "Central Configuration3"}, {"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-4000, 5200], "id": "09a67cf3-f046-4209-8266-5e0424dfa616", "name": "Schedule Trigger2"}, {"parameters": {"content": "## Update Engagement for posts", "height": 480, "width": 2720}, "type": "n8n-nodes-base.stickyNote", "position": [-4100, 5040], "typeVersion": 1, "id": "69b6682d-a76f-44eb-aa9c-6fe8c5879a90", "name": "Sticky Note2"}], "connections": {"Manual Trigger": {"main": [[{"node": "Central Configuration", "type": "main", "index": 0}]]}, "Central Configuration": {"main": [[{"node": "Split Sources for Processing", "type": "main", "index": 0}]]}, "Split Sources for Processing": {"main": [[{"node": "Get Feed Output1", "type": "main", "index": 0}]]}, "Feeds Processing Complete": {"main": [[{"node": "Get scraped posts", "type": "main", "index": 0}]]}, "Filter Recent Posts": {"main": [[{"node": "Limit posts to generate topics based on", "type": "main", "index": 0}]]}, "Format output": {"main": [[{"node": "Clear Existing Topics", "type": "main", "index": 0}]]}, "Get Feed Output1": {"main": [[{"node": "Summarize Posts", "type": "main", "index": 0}]]}, "Mistral Cloud Chat Model8": {"ai_languageModel": [[{"node": "Summarize Posts", "type": "ai_languageModel", "index": 0}]]}, "Mistral Cloud Chat Model9": {"ai_languageModel": [[{"node": "Generate Topics Based On Configuration", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Generate Topics Based On Configuration", "type": "ai_outputParser", "index": 0}]]}, "Summarize Posts": {"main": [[{"node": "Format Output", "type": "main", "index": 0}]]}, "Format Output": {"main": [[{"node": "Save scraped posts", "type": "main", "index": 0}]]}, "Save scraped posts": {"main": [[{"node": "Feeds Processing Complete", "type": "main", "index": 0}]]}, "Get scraped posts": {"main": [[{"node": "Filter <PERSON> Posts", "type": "main", "index": 0}]]}, "Clear Existing Topics": {"main": [[{"node": "Add new trending topics", "type": "main", "index": 0}]]}, "Add new trending topics": {"main": [[]]}, "Limit posts to generate topics based on": {"main": [[{"node": "Generate Topics Based On Configuration", "type": "main", "index": 0}]]}, "Generate Topics Based On Configuration": {"main": [[{"node": "Format output", "type": "main", "index": 0}]]}, "Group All Variations": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Send Approval Request": {"main": [[{"node": "IF Approved?1", "type": "main", "index": 0}]]}, "Prepare Posting Tasks": {"main": [[{"node": "Post to Social Media", "type": "main", "index": 0}]]}, "Post to Social Media": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Log Posted Content": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Single User or Multi User?1": {"main": [[{"node": "Set Single User1", "type": "main", "index": 0}], [{"node": "Set Multiple Users1", "type": "main", "index": 0}]]}, "Set Single User1": {"main": [[{"node": "Get User Posts1", "type": "main", "index": 0}]]}, "Set Multiple Users1": {"main": [[{"node": "Get User Posts1", "type": "main", "index": 0}]]}, "Get User Posts1": {"main": [[{"node": "Process Data1", "type": "main", "index": 0}]]}, "Process Data1": {"main": [[{"node": "Get Trending Topics1", "type": "main", "index": 0}]]}, "Route to AI Agent1": {"main": [[{"node": "AI Agent - Single User Style1", "type": "main", "index": 0}], [{"node": "AI Agent - Multi User Style1", "type": "main", "index": 0}]]}, "AI Agent - Single User Style1": {"main": [[{"node": "Format Post", "type": "main", "index": 0}]]}, "AI Agent - Multi User Style1": {"main": [[{"node": "AI Agent - Style Merger1", "type": "main", "index": 0}]]}, "AI Agent - Style Merger1": {"main": [[{"node": "Save Generated Posts3", "type": "main", "index": 0}]]}, "Mistral Cloud Chat Model": {"ai_languageModel": [[{"node": "AI Agent - Single User Style1", "type": "ai_languageModel", "index": 0}]]}, "Format Post": {"main": [[{"node": "Save Generated Posts3", "type": "main", "index": 0}]]}, "Save Generated Posts3": {"main": [[{"node": "Group All Variations", "type": "main", "index": 0}]]}, "IF Approved?1": {"main": [[{"node": "Prepare Posting Tasks", "type": "main", "index": 0}]]}, "Mistral Cloud Chat Model10": {"ai_languageModel": [[{"node": "AI Agent - Multi User Style1", "type": "ai_languageModel", "index": 0}]]}, "Mistral Cloud Chat Model11": {"ai_languageModel": [[{"node": "AI Agent - Style Merger1", "type": "ai_languageModel", "index": 0}]]}, "Get Trending Topics1": {"main": [[{"node": "Route to AI Agent1", "type": "main", "index": 0}]]}, "Schedule Trigger1": {"main": [[{"node": "Central Configuration2", "type": "main", "index": 0}]]}, "Central Configuration2": {"main": [[{"node": "Single User or Multi User?1", "type": "main", "index": 0}]]}, "Structured Output Parser3": {"ai_outputParser": [[{"node": "AI Agent - Single User Style1", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser4": {"ai_outputParser": [[{"node": "AI Agent - Style Merger1", "type": "ai_outputParser", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Send Approval Request", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Log Posted Content", "type": "main", "index": 0}]]}, "Split Profiles": {"main": [[{"node": "Scrape Facebook Posts", "type": "main", "index": 0}]]}, "Scrape Facebook Posts": {"main": [[{"node": "Process Scraped Posts", "type": "main", "index": 0}]]}, "Process Scraped Posts": {"main": [[{"node": "Rate Limit Delay", "type": "main", "index": 0}]]}, "Rate Limit Delay": {"main": [[{"node": "Read Google Sheet", "type": "main", "index": 0}]]}, "Read Google Sheet": {"main": [[{"node": "Compare & Determine Action", "type": "main", "index": 0}]]}, "Compare & Determine Action": {"main": [[{"node": "Filter Actions", "type": "main", "index": 0}]]}, "Filter Actions": {"main": [[{"node": "Update Existing Row", "type": "main", "index": 0}]]}, "Update Existing Row": {"main": [[{"node": "Sheet Update Delay", "type": "main", "index": 0}]]}, "Sheet Update Delay": {"main": [[{"node": "Generate Summary Report", "type": "main", "index": 0}]]}, "Central Configuration3": {"main": [[{"node": "Split Profiles", "type": "main", "index": 0}]]}, "Schedule Trigger2": {"main": [[{"node": "Central Configuration3", "type": "main", "index": 0}]]}}, "pinData": {"AI Agent - Single User Style1": [{"output": [{"content": "🚀 Ever hit that frustrating 'Out of Memory' error while working on a massive AI dataset? Fear not, because Python has some amazing tricks up its sleeve to help you conquer this challenge! 💪\n\nFrom memory-efficient libraries like Dask and Vaex to smart chunking strategies, there are so many ways to keep your workflow smooth and efficient. Whether you're a data scientist or an AI engineer, these tools can be a game-changer for handling big data like a pro. 🌟\n\nWhat’s your go-to solution for memory issues? Share your tips below! 👇 #AITips #PythonHacks #BigData #DataScience", "variation": "Positive", "topic": "Overcoming Out-of-Memory Challenges in AI: Python-Based Solutions"}, {"content": "Ugh, out-of-memory errors—every AI engineer’s worst nightmare! 😫 No matter how powerful your machine is, those massive datasets always find a way to crash your progress. Python’s supposed to be the solution, but sometimes it feels like it’s just adding to the problem. \n\nChunking? Slow. Dask? Overkill. Vaex? Too complex. Why does handling big data have to be such a headache? 🤯\n\nAnyone else feel like they’re constantly fighting their tools instead of actually getting work done? #AIStruggles #MemoryIssues #DataScienceProblems", "variation": "Negative", "topic": "Overcoming Out-of-Memory Challenges in AI: Python-Based Solutions"}, {"content": "Handling large datasets in AI often leads to out-of-memory challenges, especially when working with limited resources. Python offers several solutions to address this issue effectively. \n\nMemory-efficient libraries like Dask and Vaex allow processing of datasets larger than your system’s RAM by breaking them into manageable chunks. Additionally, techniques like lazy evaluation and optimization methods can significantly reduce memory usage. \n\nFor data scientists and AI engineers, understanding these tools and strategies is essential for efficient big data processing. Have you explored these methods? What has worked best for you?", "variation": "Neutral/objective", "topic": "Overcoming Out-of-Memory Challenges in AI: Python-Based Solutions"}, {"content": "🎉 WHO ELSE HATES OUT-OF-MEMORY ERRORS?! 🎉\n\nGood news: Python is here to save the day with some INCREDIBLE tools to handle those massive datasets like a boss! 💥 Whether it’s <PERSON><PERSON> for parallel processing, Vaex for lightning-fast operations, or chunking strategies to keep things smooth, you’ve got options to keep your AI projects running without a hitch. \n\nNo more crashes, no more frustration—just pure, unstopped productivity! 🚀 Let’s hear it: What’s your favorite memory-saving trick? Drop it in the comments! 👇 #AIWinning #PythonPower #BigDataMagic", "variation": "Enthusiastic/energetic", "topic": "Overcoming Out-of-Memory Challenges in AI: Python-Based Solutions"}, {"content": "Oh, wow, another day, another 'Out of Memory' error—how thrilling! 😒 Who knew that working with big data in AI would feel like trying to fit an elephant into a shoebox? \n\nBut hey, don’t worry, Python’s got you covered with its *totally intuitive* solutions like Dask and Vaex. Because nothing says ‘fun’ like spending hours figuring out how to chunk your data just so your laptop doesn’t explode. 🎉\n\nIsn’t AI just the best? #MemoryIssues #AIProblems #WhyDoWeDoThisToOurselves", "variation": "Sarcastic/ironic", "topic": "Overcoming Out-of-Memory Challenges in AI: Python-Based Solutions"}, {"content": "Out-of-memory challenges are a common obstacle in AI and machine learning projects, particularly when dealing with large-scale datasets. Python provides robust solutions to mitigate these issues, ensuring efficient data processing without compromising system performance. \n\nLibraries such as Dask and Vaex enable the handling of datasets larger than available RAM by leveraging chunking and lazy evaluation techniques. Additionally, optimizing data types and utilizing memory-efficient data structures can further enhance performance. \n\nFor professionals in data science and AI engineering, mastering these tools is critical for seamless big data operations. How do you approach memory optimization in your projects?", "variation": "Professional/formal", "topic": "Overcoming Out-of-Memory Challenges in AI: Python-Based Solutions"}, {"content": "Hey everyone! 👋 Ever run into that annoying ‘Out of Memory’ error while working on a big AI project? Yeah, we’ve all been there. But guess what? Python’s got some pretty cool tools to help you out! \n\nFrom libraries like Dask and Vaex to simple chunking tricks, there are plenty of ways to keep your workflow running smoothly. No need to pull your hair out—just a little tweaking, and you’re good to go! 😊\n\nWhat’s your favorite way to handle big datasets? Let’s chat in the comments! 💬 #AITips #PythonTricks #DataScienceLife", "variation": "Casual/friendly", "topic": "Overcoming Out-of-Memory Challenges in AI: Python-Based Solutions"}]}, {"output": [{"content": "🚀 **Demystifying the Bias-Variance Trade-Off: A Visual Guide for AI Practitioners!** 🚀\n\nEver felt like your machine learning model is either *too* simple or *too* complex? You’re not alone! The bias-variance trade-off is one of the most critical concepts in AI, and mastering it can make or break your model’s performance.\n\nIn this guide, we break it down with **visual aids** and **real-world examples** to help you understand:\n✅ What bias and variance *really* mean\n✅ How they impact your model’s accuracy\n✅ Practical tips to strike the *perfect* balance\n\nWhether you're a beginner or a seasoned practitioner, this is a game-changer! 💡 Let’s build models that generalize like a pro! 🎯\n\n#AIMadeSimple #MachineLearning #BiasVarianceTradeOff #DataScience #AICommunity", "variation": "Positive", "topic": "Demystifying the Bias-Variance Trade-Off: A Visual Guide for AI Practitioners"}, {"content": "😤 **The Bias-Variance Trade-Off: Why Does It Feel Like a Never-Ending Struggle?**\n\nLet’s be real—balancing bias and variance in machine learning is *exhausting*. No matter what you do, your model is either underfitting or overfitting, and it feels like you’re stuck in a loop of frustration. 😩\n\nSure, the theory sounds simple: find the sweet spot. But in practice? It’s a nightmare. You tweak hyperparameters, add more data, or simplify the model, and *still*, something’s off.\n\nThis guide dives into the chaos with visuals and examples, but let’s be honest—it’s a band-aid for a deeper issue. When will we get tools that *actually* make this easier? 🤔\n\n#MLStruggles #BiasVarianceNightmare #AIProblems", "variation": "Negative", "topic": "Demystifying the Bias-Variance Trade-Off: A Visual Guide for AI Practitioners"}, {"content": "📊 **Understanding the Bias-Variance Trade-Off in Machine Learning**\n\nThe bias-variance trade-off is a fundamental concept in machine learning that influences how well a model generalizes to unseen data. High bias can lead to underfitting, while high variance can result in overfitting. Striking the right balance is essential for building robust models.\n\nIn this visual guide, we explore:\n- The definitions of bias and variance\n- Their impact on model performance\n- Strategies to manage the trade-off effectively\n\nThis resource is designed for AI practitioners looking to refine their models and improve generalization. Check it out to gain a clearer understanding of this critical concept.\n\n#MachineLearning #DataScience #BiasVarianceTradeOff #AI", "variation": "Neutral/objective", "topic": "Demystifying the Bias-Variance Trade-Off: A Visual Guide for AI Practitioners"}, {"content": "🔥 **Bias-Variance Trade-Off: The Secret Sauce to Unstoppable AI Models!** 🔥\n\nOhhh, you *absolutely* need to see this! 🎉 The bias-variance trade-off isn’t just some boring theory—it’s the *key* to unlocking models that perform like magic! 🎩✨\n\nImagine this:\n- **Low bias?** Your model is *too* rigid—like a robot that can’t adapt. ❌\n- **High variance?** It’s *overfitting* like crazy—memorizing data instead of learning. ❌\n\nBut when you *nail* the balance? BOOM! 💥 Your model generalizes like a champ! 🏆\n\nWe’ve got a **visual guide** packed with examples to make this *super* clear. Ready to level up your AI game? Let’s gooo! 🚀\n\n#AIWinning #MachineLearningMagic #BiasVarianceTradeOff #DataScienceRocks", "variation": "Enthusiastic/energetic", "topic": "Demystifying the Bias-Variance Trade-Off: A Visual Guide for AI Practitioners"}, {"content": "🤨 **Oh Wow, Another Guide on the Bias-Variance Trade-Off. How *Original*.**\n\nSo, you’re telling me that after *years* of AI research, the best we can do is *still* talk about bias and variance? Groundbreaking. 🙄\n\nBut sure, let’s pretend this visual guide is *revolutionary*. Because, you know, nothing says ‘cutting-edge AI’ like rehashing the same concepts with slightly prettier graphs. 📊✨\n\nPro tip: If your model isn’t working, just add more data. Or don’t. Who knows? The bias-variance trade-off is basically a fancy way of saying ‘good luck figuring it out.’ 😂\n\n#AISarcasm #BiasVarianceAgain #DataScienceMemes", "variation": "Sarcastic/ironic", "topic": "Demystifying the Bias-Variance Trade-Off: A Visual Guide for AI Practitioners"}, {"content": "📚 **A Comprehensive Examination of the Bias-Variance Trade-Off in Machine Learning**\n\nThe bias-variance trade-off represents a critical challenge in the development of machine learning models. Bias refers to the error introduced by approximating a real-world problem with a simplified model, while variance pertains to the model's sensitivity to small fluctuations in the training set. An optimal model achieves a balance between these two sources of error, thereby enhancing its predictive performance on unseen data.\n\nThis visual guide provides a detailed exploration of:\n- The theoretical underpinnings of bias and variance\n- Their respective impacts on model generalization\n- Methodologies to mitigate their adverse effects\n\nDesigned for AI practitioners, this resource aims to facilitate a deeper comprehension of this pivotal concept, thereby enabling the creation of more robust and generalizable models.\n\n#MachineLearningTheory #DataScienceResearch #BiasVarianceAnalysis", "variation": "Professional/formal", "topic": "Demystifying the Bias-Variance Trade-Off: A Visual Guide for AI Practitioners"}, {"content": "Hey everyone! 👋\n\nEver scratched your head wondering why your model just *won’t* behave? 🤔 The bias-variance trade-off might be the culprit! It’s all about finding that sweet spot where your model isn’t *too* simple or *too* complex.\n\nWe’ve put together a **visual guide** to help you wrap your head around it—no PhD required! 😉 Inside, you’ll find:\n- Easy-to-understand explanations\n- Real-world examples (because who likes abstract theory?)\n- Tips to tweak your models like a pro\n\nGive it a read and let us know: What’s your go-to trick for balancing bias and variance? Drop your thoughts below! 👇\n\n#AIFriends #MachineLearningTips #BiasVarianceTradeOff #KeepItSimple", "variation": "Casual/friendly", "topic": "Demystifying the Bias-Variance Trade-Off: A Visual Guide for AI Practitioners"}]}]}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "709ebd16ffaf7216b63eac9848a2e29f718447049ed751cf3b7801ce369ccd9d"}}