import express from 'express';
import Node<PERSON><PERSON>ch<PERSON>ache, { FileSystemCache } from 'node-fetch-cache';
import Parser from 'rss-parser';
import puppeteer from 'puppeteer';
import 'dotenv/config';
import { newInjectedPage } from 'fingerprint-injector';
import { JSDOM } from 'jsdom';
import { setTimeout } from 'timers/promises';

const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(express.json({ limit: '10mb' }));

// === Cache Setup ===
const fetch = NodeFetchCache.create({
  cache: new FileSystemCache({
    cacheDirectory: './cache',
    ttl: 1000 * 60 * 60 * 24 * 30, // 30 days
  }),
  shouldCacheResponse: (response) => response.ok,
});

const apiKey = process.env.APIFY_API_TOKEN;

// === Types ===
interface ScraperResult {
  id?: string;
  title: string;
  link: string;
  content: string;
  pubDate: string;
  originalUrl: string;
  provider: string;
  engagement?: {
    likes?: number;
    comments?: number;
    shares?: number;
  };
}

interface PostData {
  text?: string;
  image?: string;
  link?: string;
  title?: string;
  cookies?: any[];
  credentials?: {
    username?: string;
    password?: string;
  };
}

// === Scraper Functions ===
const scrapers: Record<
  string,
  (profileUrl: string) => Promise<ScraperResult[]>
> = {
  facebook: async (profileUrl: string) => {
    if (!apiKey) {
      throw new Error('APIFY_API_TOKEN is required for Facebook scraping');
    }

    const response = await fetch(
      `https://api.apify.com/v2/acts/apify~facebook-posts-scraper/run-sync-get-dataset-items?token=${apiKey}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          proxy: { useApifyProxy: true, apifyProxyGroups: ['RESIDENTIAL'] },
          resultsLimit: 10,
          startUrls: [{ url: profileUrl }],
          maxRequestRetries: 10,
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Apify API error: ${response.statusText}`);
    }

    const json: any = await response.json();

    return json.map((item: any) => ({
      id: item.postId,
      title: item.title || '',
      link: item.url || '',
      originalUrl: profileUrl,
      content: item.text || '',
      pubDate: item.postedAt || '',
      provider: 'facebook',
      engagement: {
        likes: item.likes || 0,
        comments: item.comments || 0,
        shares: item.shares || 0,
      },
    }));
  },

  linkedin: async (profileUrl: string) => {
    if (!apiKey) {
      throw new Error('APIFY_API_TOKEN is required for LinkedIn scraping');
    }

    const response = await fetch(
      `https://api.apify.com/v2/acts/curious_coder~linkedin-post-search-scraper/run-sync-get-dataset-items?token=${apiKey}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          minDelay: 2,
          maxDelay: 8,
          proxy: { useApifyProxy: true, apifyProxyCountry: 'US' },
          urls: [profileUrl],
          limitPerSource: 20,
        }),
      }
    );

    const json: any = await response.json();

    if (!response.ok) {
      throw new Error(`Apify API error: ${response.statusText}`);
    }

    return json.map((item: any) => ({
      id: item.urn,
      title: item.title || '',
      link: item.url || '',
      originalUrl: profileUrl,
      content: item.text || '',
      pubDate: item.postedAt || '',
      provider: 'linkedin',
      engagement: {
        likes: item.numLikes || 0,
        comments: item.numComments || 0,
        shares: item.numShares || 0,
      },
    }));
  },

  rss: async (url: string) => {
    const parser = new Parser();
    const feed = await parser.parseURL(url);

    return feed.items.map((item) => {
      const dom = new JSDOM(item.content || item.contentSnippet || '');
      const textContent = dom.window.document.body.textContent || '';

      return {
        title: item.title || '',
        link: item.link || '',
        originalUrl: url,
        content: textContent.trim(),
        pubDate: item.pubDate || '',
        provider: 'rss',
      };
    });
  },
};

// === Poster Functions ===
const posters: Record<
  string,
  (
    data: PostData
  ) => Promise<{ success: boolean; message: string; postUrl?: string }>
> = {
  linkedin: async (data: PostData) => {
    return {
      success: false,
      message: `LinkedIn posting failed: Not setup yet`,
    };
  },

  facebook: async (data: PostData) => {
    const browser = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    try {
      const page = await newInjectedPage(browser, {
        fingerprintOptions: {
          devices: ['mobile'],
          operatingSystems: ['ios', 'android'],
        },
      });

      // Set cookies if provided
      if (data.cookies && data.cookies.length > 0) {
        await browser.setCookie(...data.cookies);
      }

      await page.goto('https://www.facebook.com/composer/', {
        waitUntil: 'networkidle2',
      });

      const url = await page.url();
      const needsLogin = url.includes('login');

      if (needsLogin && data.credentials) {
        throw new Error(
          'Facebook login via credentials is not supported yet. Please provide updated cookies.'
        );
      }

      // Click on the post composer
      await page.click('[aria-label="What\'s on your mind, create a post"]');
      // Wait for the text input area to be ready
      await page.keyboard.type(data.text as string, {
        delay: 10,
      });
      // Post
      const submit = await page.evaluateHandle(() => {
        return [...document.querySelectorAll('span')].find(
          (el) => el.textContent.trim() === 'POST'
        );
      });

      if (!submit) {
        throw new Error('Post button not found!');
      }

      await submit.click();

      await setTimeout(3000);

      return { success: true, message: 'Posted successfully to Facebook' };
    } catch (error: any) {
      return {
        success: false,
        message: `Facebook posting failed: ${error.message}`,
      };
    } finally {
      await browser.close();
    }
  },

  twitter: async (data: PostData) => {
    return {
      success: false,
      message: `Twitter posting failed: Not setup yet`,
    };
  },
};

// === Routes ===

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Scraping route
app.get('/scrape/:provider', async (req, res) => {
  const { provider } = req.params;
  const { url } = req.query;

  try {
    if (!scrapers[provider]) {
      return res.status(400).json({
        error: `Unknown provider: ${provider}`,
        availableProviders: Object.keys(scrapers),
      });
    }

    if (!url || typeof url !== 'string') {
      return res.status(400).json({ error: 'url query parameter is required' });
    }

    const data = await scrapers[provider](url);

    res.json({
      success: true,
      provider,
      url,
      count: data.length,
      data,
    });
  } catch (error: any) {
    console.error(`Scraping error for ${provider}:`, error);
    res.status(500).json({
      error: 'Scraping failed',
      message: error.message,
      provider,
    });
  }
});

// Posting route
app.post('/post/:provider', async (req, res) => {
  const { provider } = req.params;
  const postData: PostData = req.body;

  try {
    // Commenting out during testing, uncomment when using during production

    // if (!posters[provider]) {
    //   return res.status(400).json({
    //     error: `Unknown provider: ${provider}`,
    //     availableProviders: Object.keys(posters),
    //   });
    // }

    // if (!postData.text && !postData.image) {
    //   return res
    //     .status(400)
    //     .json({ error: 'Either text or image is required' });
    // }

    const result = {
      success: true,
      message: 'Posting is disabled in this demo.',
      postUrl: '',
    };

    if (result.success) {
      res.json({
        success: true,
        provider,
        message: result.message,
        postUrl: result.postUrl,
      });
    } else {
      res.status(500).json({
        success: false,
        provider,
        message: result.message,
      });
    }
  } catch (error: any) {
    console.error(`Posting error for ${provider}:`, error);
    res.status(500).json({
      error: 'Posting failed',
      message: error.message,
      provider,
    });
  }
});

// List available providers
app.get('/providers', (req, res) => {
  res.json({
    scrapers: Object.keys(scrapers),
    posters: Object.keys(posters),
  });
});

// === Server ===
app.listen(port, () => {
  console.log(`🚀 Social Media API running at http://localhost:${port}`);
  console.log(`📊 Available scrapers: ${Object.keys(scrapers).join(', ')}`);
  console.log(`📤 Available posters: ${Object.keys(posters).join(', ')}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully...');
  process.exit(0);
});
