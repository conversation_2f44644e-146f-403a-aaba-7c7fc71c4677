#!/bin/sh

WORKFLOW_DIR=/workflows
CREDENTIAL_DIR=/credentials

echo ">>> Importing workflows/credentials from files..."
n8n import:workflow --input=$WORKFLOW_DIR --separate --force || true

# Start n8n in the background
n8n start &
PID=$!

# When container stops, export workflows/credentials
trap 'echo ">>> Exporting workflows/credentials to files...";
      n8n export:workflow --all --output=$WORKFLOW_DIR --separate --pretty;
      exit 0' TERM INT

wait $PID
