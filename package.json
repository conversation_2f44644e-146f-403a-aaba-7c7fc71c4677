{"name": "social-media-generator", "version": "1.0.0", "description": "Facebook posts scraper using Google Sheets integration", "main": "src/index.ts", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "tsx src/index.ts", "watch": "tsc --watch"}, "dependencies": {"@langchain/community": "^0.3.49", "@langchain/core": "^0.3.66", "@langchain/google-genai": "^0.2.16", "@langchain/mistralai": "^0.2.1", "@langchain/openai": "^0.6.3", "@types/express": "^5.0.3", "@types/node-fetch-cache": "^3.0.5", "@types/puppeteer": "^7.0.4", "@types/uuid": "^10.0.0", "dotenv": "^16.3.1", "expess": "0.0.1-security", "fingerprint-injector": "^2.1.70", "fs-extra": "^11.1.1", "googleapis": "^128.0.0", "jsdom": "^26.1.0", "langchain": "^0.3.30", "node-fetch-cache": "^5.0.2", "puppeteer": "^24.17.0", "rss-parser": "^3.13.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/jsdom": "^21.1.7", "@types/node": "^20.10.0", "nodemon": "^3.0.2", "ts-node": "^10.9.0", "tsx": "^4.20.3", "typescript": "^5.3.0"}, "type": "module", "keywords": ["facebook", "scraper", "google-sheets", "apify", "typescript"], "author": "", "license": "MIT", "packageManager": "pnpm@9.13.0+sha512.beb9e2a803db336c10c9af682b58ad7181ca0fbd0d4119f2b33d5f2582e96d6c0d93c85b23869295b765170fbdaa92890c0da6ada457415039769edf3c959efe"}